import js from "@eslint/js";
import tseslint from "typescript-eslint";

export default [
    js.configs.recommended,
    ...tseslint.configs.recommendedTypeChecked,
    {
        files: ["**/*.{js,mjs,cjs,ts}"],
        languageOptions: {
            parserOptions: {
                project: "./tsconfig.json",
                tsconfigRootDir: import.meta.dirname,
            },
        },
        rules: {
            // TypeScript specific rules
            "@typescript-eslint/no-unused-vars": [
                "error",
                {
                    argsIgnorePattern: "^_",
                    varsIgnorePattern: "^_",
                    caughtErrorsIgnorePattern: "^_",
                },
            ],
            "@typescript-eslint/no-explicit-any": "warn",
            "@typescript-eslint/explicit-function-return-type": "off",
            "@typescript-eslint/explicit-module-boundary-types": "off",
            "@typescript-eslint/no-inferrable-types": "off",
            "@typescript-eslint/no-non-null-assertion": "warn",
            "@typescript-eslint/prefer-nullish-coalescing": "error",
            "@typescript-eslint/prefer-optional-chain": "error",
            "@typescript-eslint/no-unnecessary-type-assertion": "error",
            "@typescript-eslint/no-floating-promises": "error",
            "@typescript-eslint/await-thenable": "error",
            "@typescript-eslint/no-misused-promises": "error",

            // General JavaScript rules
            "no-console": "warn",
            "no-debugger": "error",
            "no-unused-vars": "off", // Turn off base rule as it can report incorrect errors
            "prefer-const": "error",
            "no-var": "error",
            eqeqeq: ["error", "always"],
            curly: ["error", "all"],
            "no-throw-literal": "error",
        },
    },
    {
        files: ["**/*.js"],
        ...tseslint.configs.disableTypeChecked,
        rules: {
            "@typescript-eslint/no-var-requires": "off",
        },
    },
    {
        ignores: [
            "node_modules/**",
            "dist/**",
            "build/**",
            "*.min.js",
            "coverage/**",
            "auth_info_baileys/**",
            "session/**",
            "session_backup/**",
            ".env*",
            "bun.lockb",
        ],
    },
];
