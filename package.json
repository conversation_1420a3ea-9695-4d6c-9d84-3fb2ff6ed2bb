{"name": "personal-ai-bot-v2", "version": "1.0.0", "description": "", "main": "src/index.ts", "scripts": {"dev": "<PERSON>ra dev", "start": "mastra start", "whatsapp": "bun run --watch whatsapp-server.ts", "lint": "eslint .", "lint:fix": "eslint . --fix", "format": "prettier --write .", "type-check": "tsc --noEmit"}, "keywords": [], "author": "", "license": "ISC", "type": "module", "engines": {"node": ">=20.9.0", "bun": ">=1.0.0"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@hapi/boom": "^10.0.1", "@hono/node-server": "^1.18.0", "@mastra/core": "^0.12.0", "@mastra/fastembed": "^0.10.1", "@mastra/libsql": "^0.12.0", "@mastra/loggers": "^0.10.5", "@mastra/memory": "^0.12.0", "@types/qrcode": "^1.5.5", "baileys": "^6.7.18", "hono": "^4.8.10", "qrcode": "^1.5.4", "zod": "^4.0.14"}, "devDependencies": {"@types/node": "^24.1.0", "@typescript-eslint/eslint-plugin": "^8.38.0", "@typescript-eslint/parser": "^8.38.0", "bun-types": "^1.2.19", "eslint": "^9.32.0", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "mastra": "^0.10.16", "prettier": "^3.6.2", "typescript": "^5.8.3"}, "trustedDependencies": ["protobufjs"]}