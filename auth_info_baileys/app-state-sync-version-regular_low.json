{"version": 14, "hash": {"type": "<PERSON><PERSON><PERSON>", "data": "IjeJSQtto4bLiTlcPmTFlvtOZZAyvYIMS44CH6DhKhdHfxtV43DjSbwmxMcLzvmUjuNryhup9lrlAzgll9Yh6BZbAjEbRTYQiPhr1exmk/j07Ejf/xTUOJjKKZG178V0Gn3xkgQZsaJV2bXOOC0/sseP2uP4FjBt7DJ8qqSPa0U="}, "indexValueMap": {"PS7za5zsB6q478Kw8GEh8QqiyW8UziPZRenq5q2PAII=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "31zCNGv5QuyL/v0MNKV9ok3Mr6MJucmEHxwPy7Pgw8I="}}, "XeOOZDV6QvI99kxw7nSM+NtdYuQUGIcuBHbtLh8JmcQ=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "osYR00RcfX2jj4qnKSSyQcbhoa3ZEw/FX+F8Qg292Lg="}}, "iiZYbAlnmmpWnhS4fQfMFUfpWuNimi2QwdhawExs8G4=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "5vZRt86gUcnZXx3GRQ5Z3zjNbaaoELmCCbXgAq4BYGk="}}, "lVclI6fZG84pCveB2IjJ1se3KpdKnz4Pg3zgTHsJ+x0=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "KKcCwhYMgf0GE+EdofSOnWdu42EEprowKGqCtf0lrzI="}}, "xwzImMXowIyJ0rtjeatj0ILf5zFMqj1DRIxrjHd9Xo4=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "NaNOYgeZECzLB3n0WelXsk0F1BYk6FYtalseQFkZNSU="}}, "6sLElD4+kcr/DygSq/ZpF4oV46uhxgW/FnMgXeMPcjc=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "NYgobHYEKpwXE1YYAUJcIPFXfCFYqf5K10fVvitApio="}}, "8XqfG9IOtadKEWVskSNgLNUTry0KzcnDhDxizsyuDwg=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "bSTBTVrZFlSW079GSA99qD1eAKWH7LjXnagYkCiEEv4="}}, "RUC7PIENGr6oOUVRDMhwV9GMM0AyD24l33r+vibQUiE=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "478x74JyesKseHDyrusJe0HQ8ahjA/JdK+Xz7ugIpk0="}}}}