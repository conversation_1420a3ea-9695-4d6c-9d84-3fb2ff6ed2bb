{"version": 4, "hash": {"type": "<PERSON><PERSON><PERSON>", "data": "gu+6FuKhR2dZASjBRmqcoogkuGgWaKtZ0Hx2kea5q5m0IQSOuTxT/bRo2OgzdGebChmjRW8WKiopDP9n4oUXxvXir5oXPrfmnIqekQ6H0hAs44PuxH8pHfQ2OC8U0EU/yzMY6fhzLcN/mA9PzPoubGg11ZfImgHf+ODjHPdXXNo="}, "indexValueMap": {"CGE3Nu1cXuTl4nWzX4A4D9JhIsDvlrIFMMkfTSkQmCU=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "v4MvFzGCnaJmjJu4TKIseVEHtl0f19oAPrQoFNF0a0A="}}, "ff83hofy9nIDyk3wBtxEP7qvQbG9VarPR/445sqcYBg=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "h89nnx/YiD1puwbFvhYFUoRJC5cwtX+mvgbNN+kWXcM="}}, "k4DO6z3n0PyMNq/JwM0XuO4n2oyheBKBjI9MB5fr44s=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "A+oWIXqCs7kOKIWnees847UMu0V6+V3c8V6MivKn5uk="}}, "qZPkmE5z0ncXfuS10JDNfzWh4A7pLV+byAvHlBh2fC8=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "Dqi3+n8xnDsqsBHaf3TzipnxxkFS3hGIV8PPijDkMfI="}}, "yjaWjyWfZZ0DvHbB18ZD0jmBuQLBoyAqw89hiCJEFCo=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "tkk/oxXEV7kdNPNxHBpO7r7xNYobdw2QDmkntrp2ZBg="}}, "0DI2hubmWPp6DppSZPhF33bkhB0Y5FEjs9IQgbXeH8g=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "xN1W9kI/I4pN4T34qGGu3EIymCaWYN/8c18maK2tTHk="}}, "7kKCnDHZP8O3qL1kq0nMatO73ey04U6QP5uTlSVYJfE=": {"valueMac": {"type": "<PERSON><PERSON><PERSON>", "data": "0SLw4oXL6RyqZFHvdyrdPOhRFBoB626KourOg9vI+TQ="}}}}