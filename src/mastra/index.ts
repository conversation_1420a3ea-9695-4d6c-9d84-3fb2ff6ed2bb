import { <PERSON><PERSON> } from '@mastra/core/mastra';
import { PinoLogger } from '@mastra/loggers';
import { LibSQLStore } from '@mastra/libsql';
import { chatbotAgent } from './agents/chatbot-agent';

export const mastra = new Mastra({
  workflows: {},
  agents: { chatbotAgent },
  storage: new LibSQLStore({
    // stores telemetry, evals, ... into persistent file storage
    url: 'file:./mastra.db',
  }),
  logger: new PinoLogger({
    name: '<PERSON><PERSON>',
    level: 'info',
  }),
});
