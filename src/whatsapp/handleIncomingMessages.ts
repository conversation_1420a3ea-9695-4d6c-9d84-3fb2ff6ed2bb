import makeWASocket, { type MessageUpsertType, type WAMessage, isJidBroadcast, isJidStatusBroadcast } from "baileys";

export async function handleIncomingMessages(
    { messages, type }: { messages: WAMessage[]; type: MessageUpsertType },
    sock: ReturnType<typeof makeWASocket>
) {
    if (type !== "notify") return;

    for (const msg of messages) {
        if (!msg.message) continue;
        if (msg.key.fromMe) continue; // Ignore messages sent by the bot
        if (isJidBroadcast(msg.key.remoteJid!) || isJidStatusBroadcast(msg.key.remoteJid!)) continue;

        const messageText = getMessageText(msg);
        if (!messageText) continue;

        console.log("Received message:", messageText, "from:", msg.key.remoteJid);

        // Extract user ID and chat ID for proper memory management
        const userJid = msg.key.participant || msg.key.remoteJid!; // User who sent the message
        const chatJid = msg.key.remoteJid!; // Chat where message was sent
        
        // Here you can integrate with your AI agent
        const response = await generateBotResponse(messageText, userJid, chatJid);

        if (response) {
            await sock.sendMessage(msg.key.remoteJid!, { text: response });
        }
    }
}

function getMessageText(msg: WAMessage): string | null {
    if (msg.message?.conversation) {
        return msg.message.conversation;
    }
    if (msg.message?.extendedTextMessage?.text) {
        return msg.message.extendedTextMessage.text;
    }
    return null;
}

async function generateBotResponse(messageText: string, userJid: string, chatJid: string): Promise<string | null> {
    try {
        const { chatbotAgent } = await import("../mastra/agents/chatbot-agent.js");

        const response = await chatbotAgent.generate(messageText, {
            resourceId: userJid, // Use user's WhatsApp ID for memory persistence
            threadId: chatJid, // Use chat ID as thread ID for conversation context
        });

        return response.text || null;
    } catch (error) {
        console.error("Error generating bot response:", error);
        return null;
    }
}
