import { Hono } from "hono";
import { serve } from "@hono/node-server";
import {
  getWhatsAppService,
  getQRCode,
  getConnectionState,
  isWhatsAppConnected,
} from "./src/whatsapp/index.js";

const app = new Hono();

// Serve static HTML for setup page
app.get("/", (c) => {
  const html = `
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WhatsApp Bot Setup</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            font-weight: bold;
        }
        .connected {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .disconnected {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .qr-container {
            text-align: center;
            margin: 20px 0;
        }
        .qr-code {
            max-width: 300px;
            height: auto;
            border: 2px solid #ddd;
            border-radius: 10px;
        }
        .instructions {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 20px;
            margin: 20px 0;
        }
        .refresh-btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
        }
        .refresh-btn:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WhatsApp Bot Setup</h1>
        <div id="status" class="status"></div>
        <div id="qr-container" class="qr-container"></div>
        <div class="instructions">
            <h3>Setup Instructions:</h3>
            <ol>
                <li>Open WhatsApp on your phone</li>
                <li>Go to Settings → Linked Devices</li>
                <li>Tap "Link a Device"</li>
                <li>Scan the QR code displayed above</li>
                <li>Your bot will be connected and ready to receive messages!</li>
            </ol>
        </div>
        <button class="refresh-btn" onclick="refreshStatus()">Refresh Status</button>
    </div>

    <script>
        async function refreshStatus() {
            try {
                const response = await fetch('/status');
                const data = await response.json();
                
                const statusDiv = document.getElementById('status');
                const qrContainer = document.getElementById('qr-container');
                
                if (data.connected) {
                    statusDiv.innerHTML = '✅ WhatsApp Connected Successfully!';
                    statusDiv.className = 'status connected';
                    qrContainer.innerHTML = '<p>Your WhatsApp bot is ready to receive messages.</p>';
                } else {
                    statusDiv.innerHTML = '❌ WhatsApp Not Connected';
                    statusDiv.className = 'status disconnected';
                    
                    if (data.qrCode) {
                        qrContainer.innerHTML = \`
                            <p>Scan this QR code with WhatsApp:</p>
                            <img src="\${data.qrCode}" alt="QR Code" class="qr-code">
                        \`;
                    } else {
                        qrContainer.innerHTML = '<p>Generating QR code...</p>';
                    }
                }
            } catch (error) {
                console.error('Error fetching status:', error);
                document.getElementById('status').innerHTML = '❌ Error connecting to server';
                document.getElementById('status').className = 'status disconnected';
            }
        }

        // Auto-refresh every 5 seconds
        setInterval(refreshStatus, 5000);
        
        // Initial load
        refreshStatus();
    </script>
</body>
</html>`;

  return c.html(html);
});

// API endpoint to get connection status and QR code
app.get("/status", async (c) => {
  const connected = isWhatsAppConnected();
  const qrCode = getQRCode();
  const connectionState = getConnectionState();

  return c.json({
    connected,
    qrCode,
    connectionState: connectionState.connection,
    lastDisconnect: connectionState.lastDisconnect?.error?.message,
  });
});

// Initialize WhatsApp service when server starts
async function startServer() {
  console.log("Initializing WhatsApp service...");
  await getWhatsAppService();

  const port = process.env.PORT || 3000;
  console.log(`Server running on http://localhost:${port}`);

  serve({
    fetch: app.fetch,
    port: Number(port),
  });
}

startServer().catch(console.error);
