# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a personal AI bot (v2) that provides WhatsApp messaging capabilities integrated with Mastra framework for AI agent functionality. The bot connects to WhatsApp using <PERSON><PERSON> library and uses Google's Gemini 2.5 Flash model for intelligent responses in group chats.

## Architecture

### Core Components

- **WhatsApp Service** (`src/whatsapp/`): Handles WhatsApp connection, QR code generation, and message processing using Baileys
- **Mastra Framework** (`src/mastra/`): AI agent orchestration with memory, storage, and conversation management
- **Web Server** (`whatsapp-server.ts`): Hono-based server providing setup UI and status API endpoints
- **Chatbot Agent** (`src/mastra/agents/chatbot-agent.ts`): Main AI agent with persistent memory and conversation context

### Data Flow

1. WhatsApp messages are received via Baileys WebSocket connection
2. Messages are processed in `handleIncomingMessages.ts`
3. Text is sent to the <PERSON>stra chatbot agent for response generation
4. Agent uses LibSQL for persistent memory and conversation history
5. Generated responses are sent back through WhatsApp

### Key Technologies

- **Baileys**: WhatsApp Web API library for message handling
- **Mastra**: AI agent framework with memory and tool capabilities
- **Hono**: Web framework for the setup server
- **LibSQL**: Local database for agent memory and conversation persistence
- **Google Gemini 2.5 Flash**: LLM model for response generation

## Development Commands

### Primary Commands

- `bun run whatsapp` - Start the WhatsApp bot with hot reload
- `bun run dev` - Start Mastra development server
- `bun run start` - Start Mastra production server

### Code Quality

- `bun run lint` - Run ESLint checks
- `bun run lint:fix` - Auto-fix ESLint issues
- `bun run format` - Format code with Prettier
- `bun run type-check` - Run TypeScript type checking

### Setup Process

1. Run `bun run whatsapp` to start the bot server
2. Visit `http://localhost:3000` to access the setup UI
3. Scan the displayed QR code with WhatsApp to link the device
4. Bot will automatically handle incoming messages once connected

## Configuration

### Config Files

- `config.json`: Bot name, owner phone numbers, command prefixes, database settings
- `tsconfig.json`: Strict TypeScript configuration with ES2022 target
- `eslint.config.js`: Comprehensive ESLint rules including TypeScript-specific checks

### Environment

- Uses Bun runtime (>=1.0.0) and Node.js (>=20.9.0)
- Authentication data stored in `auth_info_baileys/` directory
- Persistent storage in `mastra.db` SQLite database

## Memory System

The chatbot agent uses Mastra's Memory system with:

- **LibSQLStore**: Persistent conversation storage
- **LibSQLVector**: Vector database for semantic search
- **Working Memory**: Group profiles and preferences across conversations
- **Semantic Recall**: Retrieves contextually relevant past messages (topK: 3, messageRange: 2)
- **Conversation History**: Maintains last 20 messages per thread

## Important Notes

- WhatsApp connection state is managed globally with automatic reconnection
- All messages are filtered to ignore broadcasts and bot's own messages
- Agent responses are contextual based on conversation history and user memory
- The setup server provides real-time connection status and QR code generation
