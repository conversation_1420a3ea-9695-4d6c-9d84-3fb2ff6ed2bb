{"compilerOptions": {"target": "ES2022", "lib": ["ES2022"], "module": "ES2022", "moduleResolution": "bundler", "allowImportingTsExtensions": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "forceConsistentCasingInFileNames": true, "strict": true, "noUnusedLocals": true, "noUnusedParameters": true, "exactOptionalPropertyTypes": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "noUncheckedIndexedAccess": true, "skipLibCheck": true, "noEmit": true, "outDir": "dist", "declaration": true, "declarationMap": true, "sourceMap": true, "removeComments": false, "importHelpers": true, "isolatedModules": true, "verbatimModuleSyntax": true, "resolveJsonModule": true, "types": ["bun-types"]}, "include": ["src/**/*", "*.ts", "*.js"], "exclude": ["node_modules", "dist", "build"]}